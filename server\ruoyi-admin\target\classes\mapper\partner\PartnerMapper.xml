<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.partner.mapper.PartnerMapper">
    
    <resultMap type="Partner" id="PartnerResult">
        <result property="userId"    column="user_id"    />
        <result property="nickname"    column="nickname"    />
        <result property="traits"    column="traits"    />
        <result property="style"    column="style"    />
        <result property="description"    column="description"    />
        <result property="spontaneous"    column="spontaneous"    />
        <result property="collaborative"    column="collaborative"    />
        <result property="realist"    column="realist"    />
        <result property="logical"    column="logical"    />
        <result property="analytical"    column="analytical"    />
        <result property="introvert"    column="introvert"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectPartnerVo">
        select user_id, nickname, traits, style, description, spontaneous, collaborative, realist, logical, analytical, introvert, create_time from tb_partner
    </sql>

    <select id="selectPartnerList" parameterType="Partner" resultMap="PartnerResult">
        <include refid="selectPartnerVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="nickname != null  and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
            <if test="traits != null  and traits != ''"> and traits = #{traits}</if>
            <if test="style != null  and style != ''"> and style = #{style}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="spontaneous != null "> and spontaneous = #{spontaneous}</if>
            <if test="collaborative != null "> and collaborative = #{collaborative}</if>
            <if test="realist != null "> and realist = #{realist}</if>
            <if test="logical != null "> and logical = #{logical}</if>
            <if test="analytical != null "> and analytical = #{analytical}</if>
            <if test="introvert != null "> and introvert = #{introvert}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
        </where>
    </select>
    
    <select id="selectPartnerByUserId" parameterType="Long" resultMap="PartnerResult">
        <include refid="selectPartnerVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertPartner" parameterType="Partner">
        insert into tb_partner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="nickname != null">nickname,</if>
            <if test="traits != null">traits,</if>
            <if test="style != null">style,</if>
            <if test="description != null">description,</if>
            <if test="spontaneous != null">spontaneous,</if>
            <if test="collaborative != null">collaborative,</if>
            <if test="realist != null">realist,</if>
            <if test="logical != null">logical,</if>
            <if test="analytical != null">analytical,</if>
            <if test="introvert != null">introvert,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="nickname != null">#{nickname},</if>
            <if test="traits != null">#{traits},</if>
            <if test="style != null">#{style},</if>
            <if test="description != null">#{description},</if>
            <if test="spontaneous != null">#{spontaneous},</if>
            <if test="collaborative != null">#{collaborative},</if>
            <if test="realist != null">#{realist},</if>
            <if test="logical != null">#{logical},</if>
            <if test="analytical != null">#{analytical},</if>
            <if test="introvert != null">#{introvert},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updatePartner" parameterType="Partner">
        update tb_partner
        <trim prefix="SET" suffixOverrides=",">
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="traits != null">traits = #{traits},</if>
            <if test="style != null">style = #{style},</if>
            <if test="description != null">description = #{description},</if>
            <if test="spontaneous != null">spontaneous = #{spontaneous},</if>
            <if test="collaborative != null">collaborative = #{collaborative},</if>
            <if test="realist != null">realist = #{realist},</if>
            <if test="logical != null">logical = #{logical},</if>
            <if test="analytical != null">analytical = #{analytical},</if>
            <if test="introvert != null">introvert = #{introvert},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deletePartnerByUserId" parameterType="Long">
        delete from tb_partner where user_id = #{userId}
    </delete>

    <delete id="deletePartnerByUserIds" parameterType="String">
        delete from tb_partner where user_id in 
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
</mapper>