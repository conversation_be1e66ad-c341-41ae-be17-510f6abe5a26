package com.ruoyi.createChat.service;

import java.util.List;
import com.ruoyi.createChat.domain.CreateChat;

/**
 * 初始对话Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface ICreateChatService 
{
    /**
     * 查询初始对话
     * 
     * @param userId 初始对话主键
     * @return 初始对话
     */
    public CreateChat selectCreateChatByUserId(Long userId);

    /**
     * 查询初始对话列表
     * 
     * @param createChat 初始对话
     * @return 初始对话集合
     */
    public List<CreateChat> selectCreateChatList(CreateChat createChat);

    /**
     * 新增初始对话
     * 
     * @param createChat 初始对话
     * @return 结果
     */
    public int insertCreateChat(CreateChat createChat);

    /**
     * 修改初始对话
     * 
     * @param createChat 初始对话
     * @return 结果
     */
    public int updateCreateChat(CreateChat createChat);

    /**
     * 批量删除初始对话
     * 
     * @param userIds 需要删除的初始对话主键集合
     * @return 结果
     */
    public int deleteCreateChatByUserIds(Long[] userIds);

    /**
     * 删除初始对话信息
     * 
     * @param userId 初始对话主键
     * @return 结果
     */
    public int deleteCreateChatByUserId(Long userId);
}
