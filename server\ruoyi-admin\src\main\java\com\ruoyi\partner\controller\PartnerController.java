package com.ruoyi.partner.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.partner.domain.Partner;
import com.ruoyi.partner.service.IPartnerService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 陪伴管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/partner/partner")
public class PartnerController extends BaseController
{
    @Autowired
    private IPartnerService partnerService;

    /**
     * 查询陪伴管理列表
     */
    @PreAuthorize("@ss.hasPermi('partner:partner:list')")
    @GetMapping("/list")
    public TableDataInfo list(Partner partner)
    {
        startPage();
        List<Partner> list = partnerService.selectPartnerList(partner);
        return getDataTable(list);
    }

    /**
     * 导出陪伴管理列表
     */
    @PreAuthorize("@ss.hasPermi('partner:partner:export')")
    @Log(title = "陪伴管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Partner partner)
    {
        List<Partner> list = partnerService.selectPartnerList(partner);
        ExcelUtil<Partner> util = new ExcelUtil<Partner>(Partner.class);
        util.exportExcel(response, list, "陪伴管理数据");
    }

    /**
     * 获取陪伴管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('partner:partner:query')")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId)
    {
        return success(partnerService.selectPartnerByUserId(userId));
    }

    /**
     * 新增陪伴管理
     */
    @PreAuthorize("@ss.hasPermi('partner:partner:add')")
    @Log(title = "陪伴管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Partner partner)
    {
        return toAjax(partnerService.insertPartner(partner));
    }

    /**
     * 修改陪伴管理
     */
    @PreAuthorize("@ss.hasPermi('partner:partner:edit')")
    @Log(title = "陪伴管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Partner partner)
    {
        return toAjax(partnerService.updatePartner(partner));
    }

    /**
     * 删除陪伴管理
     */
    @PreAuthorize("@ss.hasPermi('partner:partner:remove')")
    @Log(title = "陪伴管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        return toAjax(partnerService.deletePartnerByUserIds(userIds));
    }
}
