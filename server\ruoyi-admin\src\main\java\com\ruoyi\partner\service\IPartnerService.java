package com.ruoyi.partner.service;

import java.util.List;
import com.ruoyi.partner.domain.Partner;

/**
 * 陪伴管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface IPartnerService 
{
    /**
     * 查询陪伴管理
     * 
     * @param userId 陪伴管理主键
     * @return 陪伴管理
     */
    public Partner selectPartnerByUserId(Long userId);

    /**
     * 查询陪伴管理列表
     * 
     * @param partner 陪伴管理
     * @return 陪伴管理集合
     */
    public List<Partner> selectPartnerList(Partner partner);

    /**
     * 新增陪伴管理
     * 
     * @param partner 陪伴管理
     * @return 结果
     */
    public int insertPartner(Partner partner);

    /**
     * 修改陪伴管理
     * 
     * @param partner 陪伴管理
     * @return 结果
     */
    public int updatePartner(Partner partner);

    /**
     * 批量删除陪伴管理
     * 
     * @param userIds 需要删除的陪伴管理主键集合
     * @return 结果
     */
    public int deletePartnerByUserIds(Long[] userIds);

    /**
     * 删除陪伴管理信息
     * 
     * @param userId 陪伴管理主键
     * @return 结果
     */
    public int deletePartnerByUserId(Long userId);
}
