package com.ruoyi.web.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.role.domain.Role;
import com.ruoyi.role.service.IRoleService;
import com.ruoyi.user.service.IUserService;
import com.ruoyi.web.utils.JWTUtils;
import com.ruoyi.web.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Anonymous
@RestController
@RequestMapping("/role")
public class AppRoleController {

    @Autowired
    private IRoleService roleService;
    
    @Autowired
    private IUserService userService;

    @Autowired
    private JWTUtils jwtUtils;

    /**
     * 获取角色信息
     */
    @GetMapping("/info")
    public R getPartnerInfo(@RequestHeader("Authorization") String authHeader) {
        try {
            // 验证token
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            if (token == null || !jwtUtils.validateToken(token)) {
                return R.error("用户未登录或token无效");
            }

            // 获取当前用户ID
            Long currentUserId = jwtUtils.getUserIdFromToken(token);
            if (currentUserId == null) {
                return R.error("用户未登录");
            }

            // 查询伙伴信息
            Role role = roleService.getById(currentUserId);
            if (role == null) {
                return R.error("您还没有创建角色");
            }

            return R.success(role);

        } catch (Exception e) {
            e.printStackTrace();
            return R.error("系统异常：" + e.getMessage());
        }
    }
}