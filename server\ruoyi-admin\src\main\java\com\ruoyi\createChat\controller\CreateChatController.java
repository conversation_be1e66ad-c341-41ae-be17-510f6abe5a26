package com.ruoyi.createChat.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.createChat.domain.CreateChat;
import com.ruoyi.createChat.service.ICreateChatService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 初始对话Controller
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/createChat/createChat")
public class CreateChatController extends BaseController
{
    @Autowired
    private ICreateChatService createChatService;

    /**
     * 查询初始对话列表
     */
    @PreAuthorize("@ss.hasPermi('createChat:createChat:list')")
    @GetMapping("/list")
    public TableDataInfo list(CreateChat createChat)
    {
        startPage();
        List<CreateChat> list = createChatService.selectCreateChatList(createChat);
        return getDataTable(list);
    }

    /**
     * 导出初始对话列表
     */
    @PreAuthorize("@ss.hasPermi('createChat:createChat:export')")
    @Log(title = "初始对话", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CreateChat createChat)
    {
        List<CreateChat> list = createChatService.selectCreateChatList(createChat);
        ExcelUtil<CreateChat> util = new ExcelUtil<CreateChat>(CreateChat.class);
        util.exportExcel(response, list, "初始对话数据");
    }

    /**
     * 获取初始对话详细信息
     */
    @PreAuthorize("@ss.hasPermi('createChat:createChat:query')")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId)
    {
        return success(createChatService.selectCreateChatByUserId(userId));
    }

    /**
     * 新增初始对话
     */
    @PreAuthorize("@ss.hasPermi('createChat:createChat:add')")
    @Log(title = "初始对话", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CreateChat createChat)
    {
        return toAjax(createChatService.insertCreateChat(createChat));
    }

    /**
     * 修改初始对话
     */
    @PreAuthorize("@ss.hasPermi('createChat:createChat:edit')")
    @Log(title = "初始对话", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CreateChat createChat)
    {
        return toAjax(createChatService.updateCreateChat(createChat));
    }

    /**
     * 删除初始对话
     */
    @PreAuthorize("@ss.hasPermi('createChat:createChat:remove')")
    @Log(title = "初始对话", businessType = BusinessType.DELETE)
	@DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        return toAjax(createChatService.deleteCreateChatByUserIds(userIds));
    }
}
