import request from '@/utils/request'

// 查询陪伴管理列表
export function listPartner(query) {
  return request({
    url: '/partner/partner/list',
    method: 'get',
    params: query
  })
}

// 查询陪伴管理详细
export function getPartner(userId) {
  return request({
    url: '/partner/partner/' + userId,
    method: 'get'
  })
}

// 新增陪伴管理
export function addPartner(data) {
  return request({
    url: '/partner/partner',
    method: 'post',
    data: data
  })
}

// 修改陪伴管理
export function updatePartner(data) {
  return request({
    url: '/partner/partner',
    method: 'put',
    data: data
  })
}

// 删除陪伴管理
export function delPartner(userId) {
  return request({
    url: '/partner/partner/' + userId,
    method: 'delete'
  })
}
