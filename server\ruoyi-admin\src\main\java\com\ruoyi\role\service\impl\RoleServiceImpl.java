package com.ruoyi.role.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.role.mapper.RoleMapper;
import com.ruoyi.role.domain.Role;
import com.ruoyi.role.service.IRoleService;

/**
 * 角色管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class RoleServiceImpl implements IRoleService 
{
    @Autowired
    private RoleMapper roleMapper;

    /**
     * 查询角色管理
     * 
     * @param userId 角色管理主键
     * @return 角色管理
     */
    @Override
    public Role selectRoleByUserId(Long userId)
    {
        return roleMapper.selectRoleByUserId(userId);
    }

    /**
     * 查询角色管理列表
     * 
     * @param role 角色管理
     * @return 角色管理
     */
    @Override
    public List<Role> selectRoleList(Role role)
    {
        return roleMapper.selectRoleList(role);
    }

    /**
     * 新增角色管理
     * 
     * @param role 角色管理
     * @return 结果
     */
    @Override
    public int insertRole(Role role)
    {
        return roleMapper.insertRole(role);
    }

    /**
     * 修改角色管理
     * 
     * @param role 角色管理
     * @return 结果
     */
    @Override
    public int updateRole(Role role)
    {
        return roleMapper.updateRole(role);
    }

    /**
     * 批量删除角色管理
     * 
     * @param userIds 需要删除的角色管理主键
     * @return 结果
     */
    @Override
    public int deleteRoleByUserIds(Long[] userIds)
    {
        return roleMapper.deleteRoleByUserIds(userIds);
    }

    /**
     * 删除角色管理信息
     * 
     * @param userId 角色管理主键
     * @return 结果
     */
    @Override
    public int deleteRoleByUserId(Long userId)
    {
        return roleMapper.deleteRoleByUserId(userId);
    }
}
