package com.ruoyi.user.mapper;

import java.util.List;
import com.ruoyi.user.domain.User;

/**
 * 账号管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface UserMapper 
{
    /**
     * 查询账号管理
     * 
     * @param id 账号管理主键
     * @return 账号管理
     */
    public User selectUserById(Long id);

    /**
     * 查询账号管理列表
     * 
     * @param user 账号管理
     * @return 账号管理集合
     */
    public List<User> selectUserList(User user);

    /**
     * 新增账号管理
     * 
     * @param user 账号管理
     * @return 结果
     */
    public int insertUser(User user);

    /**
     * 修改账号管理
     * 
     * @param user 账号管理
     * @return 结果
     */
    public int updateUser(User user);

    /**
     * 删除账号管理
     * 
     * @param id 账号管理主键
     * @return 结果
     */
    public int deleteUserById(Long id);

    /**
     * 批量删除账号管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserByIds(Long[] ids);
}
