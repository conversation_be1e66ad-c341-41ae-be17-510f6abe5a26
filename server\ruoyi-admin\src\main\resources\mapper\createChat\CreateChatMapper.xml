<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.createChat.mapper.CreateChatMapper">
    
    <resultMap type="CreateChat" id="CreateChatResult">
        <result property="userId"    column="user_id"    />
        <result property="msg"    column="msg"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectCreateChatVo">
        select user_id, msg, create_time from tb_create_chat
    </sql>

    <select id="selectCreateChatList" parameterType="CreateChat" resultMap="CreateChatResult">
        <include refid="selectCreateChatVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="msg != null  and msg != ''"> and msg = #{msg}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
        </where>
    </select>
    
    <select id="selectCreateChatByUserId" parameterType="Long" resultMap="CreateChatResult">
        <include refid="selectCreateChatVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertCreateChat" parameterType="CreateChat">
        insert into tb_create_chat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="msg != null">msg,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="msg != null">#{msg},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateCreateChat" parameterType="CreateChat">
        update tb_create_chat
        <trim prefix="SET" suffixOverrides=",">
            <if test="msg != null">msg = #{msg},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteCreateChatByUserId" parameterType="Long">
        delete from tb_create_chat where user_id = #{userId}
    </delete>

    <delete id="deleteCreateChatByUserIds" parameterType="String">
        delete from tb_create_chat where user_id in 
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
</mapper>