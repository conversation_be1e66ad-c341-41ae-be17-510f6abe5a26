package com.ruoyi.partner.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 陪伴管理对象 tb_partner
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public class Partner extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 昵称 */
    @Excel(name = "昵称")
    private String nickname;

    /** 特质 */
    @Excel(name = "特质")
    private String traits;

    /** 形象 */
    @Excel(name = "形象")
    private String style;

    /** 描述 */
    @Excel(name = "描述")
    private String description;

    /** 随性 */
    @Excel(name = "随性")
    private Long spontaneous;

    /** 协作 */
    @Excel(name = "协作")
    private Long collaborative;

    /** 务实 */
    @Excel(name = "务实")
    private Long realist;

    /** 逻辑 */
    @Excel(name = "逻辑")
    private Long logical;

    /** 分析 */
    @Excel(name = "分析")
    private Long analytical;

    /** 内向 */
    @Excel(name = "内向")
    private Long introvert;

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setNickname(String nickname) 
    {
        this.nickname = nickname;
    }

    public String getNickname() 
    {
        return nickname;
    }

    public void setTraits(String traits) 
    {
        this.traits = traits;
    }

    public String getTraits() 
    {
        return traits;
    }

    public void setStyle(String style)
    {
        this.style = style;
    }

    public String getStyle()
    {
        return style;
    }

    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    public void setSpontaneous(Long spontaneous) 
    {
        this.spontaneous = spontaneous;
    }

    public Long getSpontaneous() 
    {
        return spontaneous;
    }

    public void setCollaborative(Long collaborative) 
    {
        this.collaborative = collaborative;
    }

    public Long getCollaborative() 
    {
        return collaborative;
    }

    public void setRealist(Long realist) 
    {
        this.realist = realist;
    }

    public Long getRealist() 
    {
        return realist;
    }

    public void setLogical(Long logical) 
    {
        this.logical = logical;
    }

    public Long getLogical() 
    {
        return logical;
    }

    public void setAnalytical(Long analytical) 
    {
        this.analytical = analytical;
    }

    public Long getAnalytical() 
    {
        return analytical;
    }

    public void setIntrovert(Long introvert) 
    {
        this.introvert = introvert;
    }

    public Long getIntrovert() 
    {
        return introvert;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userId", getUserId())
            .append("nickname", getNickname())
            .append("traits", getTraits())
            .append("style", getStyle())
            .append("description", getDescription())
            .append("spontaneous", getSpontaneous())
            .append("collaborative", getCollaborative())
            .append("realist", getRealist())
            .append("logical", getLogical())
            .append("analytical", getAnalytical())
            .append("introvert", getIntrovert())
            .append("createTime", getCreateTime())
            .toString();
    }
}
