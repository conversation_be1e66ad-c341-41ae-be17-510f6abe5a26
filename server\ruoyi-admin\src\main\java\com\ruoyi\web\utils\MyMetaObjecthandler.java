package com.ruoyi.web.utils;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 自定义元数据对象处理器
 */
@Component
public class MyMetaObjecthandler implements MetaObjectHandler {
    /**
     * 插入操作，自动填充
     * @param metaObject
     */
    @Override
    public void insertFill(MetaObject metaObject) {
//        log.info("公共字段自动填充[insert]...");
//        log.info(metaObject.toString());

        // 检查字段是否存在再设置，避免字段不存在时出错
        if (metaObject.hasGetter("createTime")) {
            metaObject.setValue("createTime", LocalDateTime.now());
        }
        if (metaObject.hasGetter("updateTime")) {
            metaObject.setValue("updateTime", LocalDateTime.now());
        }
//        metaObject.setValue("createUser", com.kibi.common.BaseContext.getCurrentId());
//        metaObject.setValue("updateUser", com.kibi.common.BaseContext.getCurrentId());
    }

    /**
     * 更新操作，自动填充
     * @param metaObject
     */
    @Override
    public void updateFill(MetaObject metaObject) {
//        log.info("公共字段自动填充[update]...");
//        log.info(metaObject.toString());

        long id = Thread.currentThread().getId();
//        log.info("线程id为：{}",id);

        // 检查字段是否存在再设置，避免字段不存在时出错
        if (metaObject.hasGetter("updateTime")) {
            metaObject.setValue("updateTime", LocalDateTime.now());
        }
//        metaObject.setValue("updateUser", com.kibi.common.BaseContext.getCurrentId());
    }
}
