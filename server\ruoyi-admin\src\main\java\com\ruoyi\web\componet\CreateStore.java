package com.ruoyi.web.componet;

import com.ruoyi.createChat.domain.CreateChat;
import com.ruoyi.createChat.service.ICreateChatService;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.ChatMessageDeserializer;
import dev.langchain4j.data.message.ChatMessageSerializer;
import dev.langchain4j.data.message.ChatMessageType;
import dev.langchain4j.store.memory.chat.ChatMemoryStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Component
public class CreateStore implements ChatMemoryStore {

    @Autowired
    private ICreateChatService createChatService;

    @Override
    public List<ChatMessage> getMessages(Object memoryId) {
        CreateChat createChat = createChatService.getById(memoryId.toString());
        if (createChat == null) return new ArrayList<>();
        List<ChatMessage> list = ChatMessageDeserializer.messagesFromJson(createChat.getMsg());
        
        // Ensure system messages are at the beginning
        List<ChatMessage> systemMessages = new ArrayList<>();
        List<ChatMessage> otherMessages = new ArrayList<>();
        
        for (ChatMessage message : list) {
            if (message.type() == ChatMessageType.SYSTEM) {
                systemMessages.add(message);
            } else {
                otherMessages.add(message);
            }
        }
        
        // Combine with system messages first
        List<ChatMessage> orderedMessages = new ArrayList<>();
        orderedMessages.addAll(systemMessages);
        orderedMessages.addAll(otherMessages);
        
        return orderedMessages;
    }

    @Transactional
    @Override
    public void updateMessages(Object memoryId, List<ChatMessage> list) {
        CreateChat byId = createChatService.getById(memoryId.toString());
        String messagesJson = ChatMessageSerializer.messagesToJson(list);
        if (byId == null) {
            CreateChat createChat = new CreateChat();
            // Fix: Parse String to Integer instead of casting
            createChat.setUserId(Integer.parseInt(memoryId.toString()));
            createChat.setMsg(messagesJson);
            createChatService.save(createChat);
        } else {
            byId.setMsg(messagesJson);
            createChatService.updateById(byId);
        }
    }

    @Transactional
    @Override
    public void deleteMessages(Object memoryId) {
        // Implementation needed if you want to delete messages
    }
}