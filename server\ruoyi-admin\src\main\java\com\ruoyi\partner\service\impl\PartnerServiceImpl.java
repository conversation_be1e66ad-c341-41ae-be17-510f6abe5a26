package com.ruoyi.partner.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.partner.mapper.PartnerMapper;
import com.ruoyi.partner.domain.Partner;
import com.ruoyi.partner.service.IPartnerService;

/**
 * 陪伴管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class PartnerServiceImpl implements IPartnerService 
{
    @Autowired
    private PartnerMapper partnerMapper;

    /**
     * 查询陪伴管理
     * 
     * @param userId 陪伴管理主键
     * @return 陪伴管理
     */
    @Override
    public Partner selectPartnerByUserId(Long userId)
    {
        return partnerMapper.selectPartnerByUserId(userId);
    }

    /**
     * 查询陪伴管理列表
     * 
     * @param partner 陪伴管理
     * @return 陪伴管理
     */
    @Override
    public List<Partner> selectPartnerList(Partner partner)
    {
        return partnerMapper.selectPartnerList(partner);
    }

    /**
     * 新增陪伴管理
     * 
     * @param partner 陪伴管理
     * @return 结果
     */
    @Override
    public int insertPartner(Partner partner)
    {
        partner.setCreateTime(DateUtils.getNowDate());
        return partnerMapper.insertPartner(partner);
    }

    /**
     * 修改陪伴管理
     * 
     * @param partner 陪伴管理
     * @return 结果
     */
    @Override
    public int updatePartner(Partner partner)
    {
        return partnerMapper.updatePartner(partner);
    }

    /**
     * 批量删除陪伴管理
     * 
     * @param userIds 需要删除的陪伴管理主键
     * @return 结果
     */
    @Override
    public int deletePartnerByUserIds(Long[] userIds)
    {
        return partnerMapper.deletePartnerByUserIds(userIds);
    }

    /**
     * 删除陪伴管理信息
     * 
     * @param userId 陪伴管理主键
     * @return 结果
     */
    @Override
    public int deletePartnerByUserId(Long userId)
    {
        return partnerMapper.deletePartnerByUserId(userId);
    }
}
