import request from '@/utils/request'

// 查询角色管理列表
export function listRole(query) {
  return request({
    url: '/role/role/list',
    method: 'get',
    params: query
  })
}

// 查询角色管理详细
export function getRole(userId) {
  return request({
    url: '/role/role/' + userId,
    method: 'get'
  })
}

// 新增角色管理
export function addRole(data) {
  return request({
    url: '/role/role',
    method: 'post',
    data: data
  })
}

// 修改角色管理
export function updateRole(data) {
  return request({
    url: '/role/role',
    method: 'put',
    data: data
  })
}

// 删除角色管理
export function delRole(userId) {
  return request({
    url: '/role/role/' + userId,
    method: 'delete'
  })
}
