import request from '@/utils/request'

// 查询初始对话列表
export function listCreateChat(query) {
  return request({
    url: '/createChat/createChat/list',
    method: 'get',
    params: query
  })
}

// 查询初始对话详细
export function getCreateChat(userId) {
  return request({
    url: '/createChat/createChat/' + userId,
    method: 'get'
  })
}

// 新增初始对话
export function addCreateChat(data) {
  return request({
    url: '/createChat/createChat',
    method: 'post',
    data: data
  })
}

// 修改初始对话
export function updateCreateChat(data) {
  return request({
    url: '/createChat/createChat',
    method: 'put',
    data: data
  })
}

// 删除初始对话
export function delCreateChat(userId) {
  return request({
    url: '/createChat/createChat/' + userId,
    method: 'delete'
  })
}
